<view class="chart-grid-container" style="height: {{containerHeight}}px;">
  <view class="chart-grid">
    <view 
      wx:for="{{chartList}}" 
      wx:key="id" 
      class="chart-item"
      bindtap="onChartTap"
      data-item="{{item}}"
    >
      <image class="chart-image" src="{{item.image}}" mode="aspectFill" />
      <view class="chart-info">
        <text class="chart-title">{{item.title}}</text>
        <text class="chart-type">{{item.type}}</text>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view wx:if="{{chartList.length === 0}}" class="empty-state">
    <text class="empty-text">暂无图表数据</text>
  </view>
</view>
