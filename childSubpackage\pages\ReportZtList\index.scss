/* 页面容器 */
.page {
  height: 100vh;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* AI入口 */
.ai-entrance {
  background: #fff;
  padding: 20rpx 0;

  .ai-image {
    width: 750rpx;
    height: 184rpx;
    display: block;
  }
}

// 统计信息容器
.company_num_container {
  position: relative;
  z-index: 100;
}

.company_num {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  background: #fff;
  margin-top: 20rpx;
  border-bottom: 1rpx solid #eee;

  .text_left {
    font-size: 28rpx;
    font-weight: 400;
    color: #74798c;
    .one {
      font-weight: 600;
      font-size: 28rpx;
      color: #20263a;
    }

    .color_num {
      color: #e72410;
      font-weight: 600;
    }
  }

  // 排序下拉框样式
  .sort-dropdown {
    display: flex;
    align-items: center;
    cursor: pointer;

    .sort-text {
      font-size: 28rpx;
      color: #74798c;
      margin-right: 8rpx;

      &.active {
        color: #e72410;
        font-weight: 600;
      }
    }

    .sort-arrow {
      width: 24rpx;
      height: 24rpx;
      transition: transform 0.3s ease;

      &.rotate {
        transform: rotate(180deg);
      }
    }
  }
}

// 排序选项下拉列表
.sort-options {
  position: absolute;
  top: 100%;
  right: 32rpx;
  width: 296rpx;
  height: 220rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  z-index: 9999;
  overflow: visible;

  // 白色三角形指向排序文字
  &::before {
    content: "";
    position: absolute;
    top: -8rpx;
    right: 20rpx;
    width: 28rpx;
    height: 16rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 40rpx 0rpx rgba(32, 38, 58, 0.1);
    transform: rotate(45deg);
    border-top-left-radius: 4rpx;
  }

  .sort-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 296rpx;
    height: 68rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    padding: 0 24rpx;
    box-sizing: border-box;

    &.active {
      .option-text {
        font-family:
          PingFang SC,
          PingFang SC;
        font-weight: 600;
        font-size: 24rpx;
        color: #e72410;
      }
    }

    .option-text {
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #20263a;
    }

    .check-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
