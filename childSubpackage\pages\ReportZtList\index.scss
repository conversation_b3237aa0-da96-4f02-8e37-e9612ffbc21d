/* 页面容器 */
.page {
  height: 100vh;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
}

/* AI入口 */
.ai-entrance {
  background: #fff;
  padding: 20rpx 0;

  .ai-image {
    width: 750rpx;
    height: 184rpx;
    display: block;
  }
}

/* 统计信息容器 */
.company_num_container {
  position: relative;
  background: #fff;
  margin-top: 20rpx;

  .company_num {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 28rpx 32rpx;
    border-bottom: 1px solid #f0f0f0;

    .text_left {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #9b9eac;

      .one {
        font-weight: 500;
        color: #20263a;
        margin-right: 8rpx;
      }

      .color_num {
        color: #e72410;
        font-weight: 600;
        margin: 0 4rpx;
      }
    }

    .sort-dropdown {
      display: flex;
      align-items: center;
      gap: 8rpx;
      padding: 12rpx 16rpx;
      background: #f5f6f7;
      border-radius: 8rpx;

      .sort-text {
        font-size: 26rpx;
        color: #74798c;

        &.active {
          color: #e72410;
        }
      }

      .sort-arrow {
        width: 24rpx;
        height: 24rpx;
        transition: transform 0.3s ease;
      }
    }
  }

  /* 排序选项下拉框 */
  .sort-options {
    position: absolute;
    top: 100%;
    right: 32rpx;
    width: 296rpx;
    height: 220rpx;
    background: #ffffff;
    box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    z-index: 9999;
    overflow: visible;

    // 白色三角形指向排序文字
    &::before {
      content: "";
      position: absolute;
      top: -8rpx;
      right: 20rpx;
      width: 28rpx;
      height: 16rpx;
      background: #ffffff;
      box-shadow: 0rpx 0rpx 40rpx 0rpx rgba(32, 38, 58, 0.1);
      transform: rotate(45deg);
      border-top-left-radius: 4rpx;
    }

    .sort-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx 32rpx;
      height: 72rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      &.active {
        background: #fff5f4;

        .option-text {
          color: #e72410;
          font-weight: 500;
        }
      }

      .option-text {
        font-size: 28rpx;
        color: #20263a;
        line-height: 1.2;
      }

      .check-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}


