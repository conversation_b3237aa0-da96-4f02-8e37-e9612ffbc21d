<view class="page" bindtap="onPageTap">
  <!-- AI入口 -->
  <view class="ai-entrance" catchtap="onAIEntranceClick">
    <image
      class="ai-image"
      src="/image/report/r_ai_enter.png"
      mode="aspectFit"
    />
  </view>

  <!-- 统计信息和排序 -->
  <view class="company_num_container" catchtap="onContainerTap">
    <view class="company_num">
      <view class="text_left">
        <text class="one">报告</text>
        共找到
        <text class="color_num">{{totalCount || 0}}</text>
        个相关报告
      </view>
      <view class="sort-dropdown" catchtap="onSortDropdownTap">
        <text
          class="sort-text {{showSortDropdown ? 'active' : ''}}"
          >{{currentSortText}}</text
        >
        <image
          class="sort-arrow"
          src="{{showSortDropdown ? '/image/report/r_sort_a.png':'/image/report/r_sort.png'}}"
          mode="aspectFit"
        />
      </view>
    </view>

    <!-- 排序选项下拉框 -->
    <view
      wx:if="{{showSortDropdown}}"
      class="sort-options"
      catchtap="onSortOptionsContainerTap"
    >
      <view
        wx:for="{{sortOptions}}"
        wx:key="value"
        class="sort-option {{currentSort === item.value ? 'active' : ''}}"
        catchtap="onSortOptionTap"
        data-value="{{item.value}}"
        data-text="{{item.text}}"
      >
        <text class="option-text">{{item.text}}</text>
        <image
          wx:if="{{currentSort === item.value}}"
          class="check-icon"
          src="/image/report/r_gou.png"
          mode="aspectFit"
        />
      </view>
    </view>
  </view>

  <!-- 报告列表 -->
  <LibraryReportList
    id="libraryReportList"
    requestParams="{{libraryRequestParams}}"
    containerHeight="{{listScrollHeight}}"
    autoCheckVip="{{true}}"
    showRemainingInfo="{{true}}"
    bind:datachange="onLibraryDataChange"
    bind:error="onLibraryError"
    bind:reportclick="onLibraryReportClick"
  />
</view>
