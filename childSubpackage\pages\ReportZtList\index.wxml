<view>
  <!-- 布局如下 -->
  <!-- 第一行ai入口 -->
  <!-- 第二行类似
      <view  class="company_num_container">
              <view class="company_num">
                <view class="text_left">
                  <text class="one">报告</text>
                  共找到
                  <text
                    class="color_num"
                    >{{libraryTotal || libraryReportList.length || 0}}</text
                  >
                  个相关报告
                </view>
                <view class="sort-dropdown" catchtap="onSortDropdownTap">
                  <text
                    class="sort-text {{showSortDropdown ? 'active' : ''}}"
                    >{{currentSortText}}</text
                  >
                  <image
                    class="sort-arrow "
                    src="{{showSortDropdown ? '/image/report/r_sort_a.png':'/image/report/r_sort.png'}}"
                    mode="aspectFit"
                  />
                </view>
              </view>
              <view
                wx:if="{{showSortDropdown}}"
                class="sort-options"
                catchtap="onSortOptionsContainerTap"
              >
                <view
                  wx:for="{{sortOptions}}"
                  wx:key="value"
                  class="sort-option {{currentSort === item.value ? 'active' : ''}}"
                  catchtap="onSortOptionTap"
                  data-value="{{item.value}}"
                  data-text="{{item.text}}"
                >
                  <text class="option-text">{{item.text}}</text>
                  <image
                    wx:if="{{currentSort === item.value}}"
                    class="check-icon"
                    src="/image/report/r_gou.png"
                    mode="aspectFit"
                  />
                </view>
              </view>
            </view> 
    -->

  <!-- 第三行             <LibraryReportList
              requestParams="{{libraryRequestParams}}"
              containerHeight="{{listScrollHeight}}"
              autoCheckVip="{{true}}"
              showRemainingInfo="{{true}}"
              isReportTab="{{true}}"
              bind:datachange="onLibraryDataChange"
              bind:error="onLibraryError"
              bind:reportclick="onLibraryReportClick"
            >
            </LibraryReportList> -->
</view>
