import {getReportPageListApi} from '../../../service/industryApi';
import {getHeight} from '../../../utils/height';

const app = getApp();

Page({
  data: {
    // 专题信息
    topicId: '',
    topicTitle: '',
    topicSubtitle: '',

    // 报告列表相关
    reportList: [],
    totalCount: 0,

    // 排序相关
    sortOptions: [
      {value: 'time_desc', text: '时间倒序'},
      {value: 'time_asc', text: '时间正序'},
      {value: 'relevance', text: '相关度'}
    ],
    currentSort: 'time_desc',
    currentSortText: '时间倒序',
    showSortDropdown: false,

    // 高度相关
    listScrollHeight: 600,

    // 用户状态
    isLogin: false,
    isVip: false,

    // 请求参数
    libraryRequestParams: {}
  },

  onLoad(options) {
    const {id, title, subtitle} = options;

    if (!id) {
      wx.showToast({
        title: '专题ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 设置专题信息
    this.setData({
      topicId: id,
      topicTitle: title ? decodeURIComponent(title) : '专题报告',
      topicSubtitle: subtitle ? decodeURIComponent(subtitle) : '0'
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.topicTitle
    });

    // 设置请求参数
    this.setData({
      libraryRequestParams: {
        topic_id: id,
        sort: this.data.currentSort,
        type: 'topic' // 专题类型
      }
    });

    this.initData();
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    // 检查用户状态
    this.checkUserStatus();
  },

  // 初始化数据
  initData() {
    this.checkUserStatus();
  },

  // 检查用户状态
  checkUserStatus() {
    const isLogin = app.isLogin();
    const isVip = app.isVip ? app.isVip() : false;

    this.setData({
      isLogin,
      isVip
    });
  },

  // 计算高度
  calculateHeight() {
    setTimeout(() => {
      getHeight(this, ['.ai-entrance', '.company_num_container'], data => {
        const {screeHeight, res} = data;

        // AI入口高度
        const aiHeight = res[0]?.height || 0;
        // 统计信息高度（包含下拉框时的高度）
        const statsHeight = res[1]?.height || 0;

        // 如果显示下拉框，需要额外考虑下拉框高度
        let extraHeight = 0;
        if (this.data.showSortDropdown) {
          extraHeight = 220; // 下拉框高度
        }

        const listHeight =
          screeHeight - aiHeight - statsHeight - extraHeight - 40; // 40为边距

        this.setData({
          listScrollHeight: Math.max(listHeight, 300)
        });
      });
    }, 100);
  },

  // AI入口点击
  onAIEntranceClick() {
    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '提示',
      content: 'AI智能分析功能即将上线，将为您提供专题深度分析！',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 排序下拉框点击
  onSortDropdownTap() {
    this.setData(
      {
        showSortDropdown: !this.data.showSortDropdown
      },
      () => {
        // 下拉框状态变化后重新计算高度
        this.calculateHeight();
      }
    );
  },

  // 排序选项容器点击（阻止冒泡）
  onSortOptionsContainerTap() {
    // 阻止冒泡，不关闭下拉框
  },

  // 页面点击事件（关闭下拉框）
  onPageTap() {
    if (this.data.showSortDropdown) {
      this.setData(
        {
          showSortDropdown: false
        },
        () => {
          // 关闭下拉框后重新计算高度
          this.calculateHeight();
        }
      );
    }
  },

  // 容器点击事件（阻止冒泡）
  onContainerTap() {
    // 阻止冒泡到页面点击事件
  },

  // 排序选项点击
  onSortOptionTap(e) {
    const {value, text} = e.currentTarget.dataset;

    this.setData(
      {
        currentSort: value,
        currentSortText: text,
        showSortDropdown: false,
        libraryRequestParams: {
          ...this.data.libraryRequestParams,
          sort: value
        }
      },
      () => {
        // 关闭下拉框后重新计算高度
        this.calculateHeight();
      }
    );

    // 触发列表刷新
    this.refreshReportList();
  },

  // 刷新报告列表
  refreshReportList() {
    // 通过组件的refresh方法刷新数据
    const libraryComponent = this.selectComponent('#libraryReportList');
    if (libraryComponent) {
      libraryComponent.refresh();
    }
  },

  // 库数据变化回调
  onLibraryDataChange(e) {
    const {list = [], total = 0} = e.detail;
    this.setData({
      reportList: list,
      totalCount: total
    });
  },

  // 库错误回调
  onLibraryError(e) {
    console.error('库数据加载错误:', e.detail);
    wx.showToast({
      title: '数据加载失败',
      icon: 'none'
    });
  },

  // 报告点击回调
  onLibraryReportClick(e) {
    const {item} = e.detail;
    console.log('报告点击:', item);

    // LibraryReportList组件内部已经处理了报告点击逻辑
    // 这里可以添加额外的处理，比如统计等
  },

  // 分享
  onShareAppMessage() {
    return {
      title: `${this.data.topicTitle} - 专题报告`,
      path: `/childSubpackage/pages/ReportZtList/index?id=${
        this.data.topicId
      }&title=${encodeURIComponent(this.data.topicTitle)}`,
      imageUrl: '/image/report/r_ztbg.png'
    };
  }
});
