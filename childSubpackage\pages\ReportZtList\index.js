import {getReportPageListApi} from '../../../service/industryApi';
import {getShareUrl, handleShareUrl} from '../../../utils/mixin/pageShare';

const app = getApp();

// 常量配置
const CONSTANTS = {
  DEFAULT_HEIGHT: 600,
  MIN_HEIGHT: 400,
  TOAST_DURATION: 2000,
  NAVIGATE_DELAY: 1500,
  HEIGHT_CALC_DELAY: 100,

  // 排序选项
  SORT_OPTIONS: [
    {value: 'default', text: '默认排序'},
    {value: 'time', text: '时间排序'},
    {value: 'hot', text: '热度排序'}
  ],

  // 默认排序
  DEFAULT_SORT: {
    value: 'default',
    text: '默认排序'
  }
};

Page({
  data: {
    // 专题信息
    topicId: '',
    topicTitle: '',
    topicSubtitle: '',

    // 报告列表相关
    reportList: [],
    totalCount: 0,

    // 排序相关数据
    showSortDropdown: false,
    currentSort: CONSTANTS.DEFAULT_SORT.value,
    currentSortText: CONSTANTS.DEFAULT_SORT.text,
    sortOptions: CONSTANTS.SORT_OPTIONS,

    // 高度相关
    listScrollHeight: CONSTANTS.DEFAULT_HEIGHT,

    // 用户状态
    isLogin: false,
    isVip: false,

    // 请求参数
    libraryRequestParams: {}
  },

  onLoad(options) {
    // 参数验证
    if (!this.validateParams(options)) return;

    // 初始化页面数据
    this.initPageData(options);
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    this.checkUserStatus();
    handleShareUrl();
  },

  /**
   * 验证页面参数
   */
  validateParams(options) {
    const {id} = options;
    if (!id) {
      wx.showToast({
        title: '专题ID不能为空',
        icon: 'none'
      });
      setTimeout(() => wx.navigateBack(), CONSTANTS.NAVIGATE_DELAY);
      return false;
    }
    return true;
  },

  /**
   * 初始化页面数据
   */
  initPageData(options) {
    const {id, title, subtitle} = options;
    const topicTitle = title ? decodeURIComponent(title) : '专题报告';

    // 一次性设置所有数据
    this.setData({
      topicId: id,
      topicTitle,
      topicSubtitle: subtitle ? decodeURIComponent(subtitle) : '0',
      libraryRequestParams: this.buildRequestParams(id)
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({title: topicTitle});

    // 检查用户状态
    this.checkUserStatus();
  },

  /**
   * 构建请求参数
   */
  buildRequestParams(topicId) {
    return {
      topic_id: topicId,
      sort: this.data.currentSort,
      type: 'hdzk',
      keyword: ''
    };
  },

  /**
   * 检查用户状态
   */
  checkUserStatus() {
    const isLogin = app.isLogin();
    const isVip = app.isVip ? app.isVip() : false;

    this.setData({isLogin, isVip});
  },

  /**
   * 计算列表高度
   */
  calculateHeight() {
    setTimeout(() => {
      const {windowHeight} = wx.getSystemInfoSync();
      // 简化计算：窗口高度 - AI入口 - 统计信息 - 安全边距
      const estimatedHeight = windowHeight - 92 - 80 - 40;

      this.setData({
        listScrollHeight: Math.max(estimatedHeight, CONSTANTS.MIN_HEIGHT)
      });
    }, CONSTANTS.HEIGHT_CALC_DELAY);
  },

  // AI入口点击
  onAIEntranceClick() {
    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }

    wx.showModal({
      title: '提示',
      content: 'AI智能分析功能即将上线，将为您提供专题深度分析！',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 页面点击事件 - 关闭下拉框
   */
  onPageTap() {
    this.closeSortDropdown();
  },

  /**
   * 切换排序下拉框显示状态
   */
  onSortDropdownTap() {
    this.setData({
      showSortDropdown: !this.data.showSortDropdown
    });
  },

  /**
   * 排序选项容器点击 - 阻止冒泡
   */
  onSortOptionsContainerTap() {
    // catchtap 已阻止冒泡
  },

  /**
   * 选择排序选项
   */
  onSortOptionTap(e) {
    const {value, text} = e.currentTarget.dataset;

    // 如果选择相同选项，直接关闭
    if (value === this.data.currentSort) {
      this.closeSortDropdown();
      return;
    }

    // 更新排序并关闭下拉框
    this.updateSort(value, text);
  },

  /**
   * 关闭排序下拉框
   */
  closeSortDropdown() {
    if (this.data.showSortDropdown) {
      this.setData({showSortDropdown: false});
    }
  },

  /**
   * 更新排序方式
   */
  updateSort(value, text) {
    this.setData({
      currentSort: value,
      currentSortText: text,
      showSortDropdown: false,
      libraryRequestParams: {
        ...this.data.libraryRequestParams,
        sort: value
      }
    });
  },

  /**
   * 撼地智库数据变化回调
   */
  onLibraryDataChange(e) {
    const {list = [], total = 0} = e.detail;
    this.setData({
      reportList: list,
      totalCount: total
    });
  },

  /**
   * 撼地智库错误回调
   */
  onLibraryError(e) {
    console.error('撼地智库数据加载失败:', e.detail);
    this.showErrorToast('数据加载失败，请稍后重试');
  },

  /**
   * 报告点击回调
   */
  onLibraryReportClick(e) {
    const {item} = e.detail;
    console.log('报告点击:', item);
    // 可以在这里添加统计埋点等逻辑
  },

  /**
   * 显示错误提示
   */
  showErrorToast(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: CONSTANTS.TOAST_DURATION
    });
  }
});
