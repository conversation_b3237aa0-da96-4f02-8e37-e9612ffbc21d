<view class="page">
  <!-- 第一行文字 居中 -->
  <view class="header-text">
    <text class="header-title">研报图表</text>
  </view>

  <!-- 第二行 写死的 input输入框 点击跳转 -->
  <view class="search-container">
    <view class="search-box" bindtap="onSearchTap">
      <image
        class="search-icon"
        src="/image/report/r_search.png"
        mode="aspectFit"
      />
      <text class="search-placeholder">搜索图表内容</text>
    </view>
  </view>

  <!-- 第三行 研报入口 参考pages/report/report的zt-container部分 -->
  <view class="zt-container">
    <view class="zt-item" bindtap="onZtItemTap" data-type="chart_analysis">
      <image
        class="zt-icon"
        src="/image/report/r_chart_icon.png"
        mode="aspectFit"
      />
      <view class="zt-content">
        <text class="zt-title">图表分析</text>
        <text class="zt-desc">智能图表解读</text>
      </view>
    </view>

    <view class="zt-item" bindtap="onZtItemTap" data-type="data_visual">
      <image
        class="zt-icon"
        src="/image/report/r_data_icon.png"
        mode="aspectFit"
      />
      <view class="zt-content">
        <text class="zt-title">数据可视化</text>
        <text class="zt-desc">多维度展示</text>
      </view>
    </view>

    <view class="zt-item" bindtap="onZtItemTap" data-type="trend_chart">
      <image
        class="zt-icon"
        src="/image/report/r_trend_icon.png"
        mode="aspectFit"
      />
      <view class="zt-content">
        <text class="zt-title">趋势图表</text>
        <text class="zt-desc">趋势变化分析</text>
      </view>
    </view>

    <view class="zt-item" bindtap="onZtItemTap" data-type="compare_chart">
      <image
        class="zt-icon"
        src="/image/report/r_compare_icon.png"
        mode="aspectFit"
      />
      <view class="zt-content">
        <text class="zt-title">对比图表</text>
        <text class="zt-desc">数据对比展示</text>
      </view>
    </view>
  </view>

  <!-- 第四行 图表列表组件 一行占2个图片 -->
  <view class="chart-list-container">
    <ChartGridList
      chartList="{{chartList}}"
      containerHeight="{{listScrollHeight}}"
      bind:chartclick="onChartClick"
    />
  </view>
</view>
