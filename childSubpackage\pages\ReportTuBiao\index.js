const app = getApp();

Page({
  data: {
    // 图表列表数据
    chartList: [],
    listScrollHeight: 600,
    
    // 用户状态
    isLogin: false,
    isVip: false
  },

  onLoad(options) {
    this.initData();
  },

  onReady() {
    this.calculateHeight();
  },

  onShow() {
    this.checkUserStatus();
  },

  /**
   * 初始化数据
   */
  initData() {
    this.checkUserStatus();
    this.loadChartData();
  },

  /**
   * 检查用户状态
   */
  checkUserStatus() {
    const isLogin = app.isLogin();
    const isVip = app.isVip ? app.isVip() : false;
    this.setData({isLogin, isVip});
  },

  /**
   * 加载图表数据（模拟数据）
   */
  loadChartData() {
    // 模拟图表数据
    const mockChartList = [
      {
        id: 1,
        title: '2024年市场趋势分析',
        image: '/image/report/chart1.png',
        type: 'line'
      },
      {
        id: 2,
        title: '行业对比数据',
        image: '/image/report/chart2.png',
        type: 'bar'
      },
      {
        id: 3,
        title: '市场份额分布',
        image: '/image/report/chart3.png',
        type: 'pie'
      },
      {
        id: 4,
        title: '增长率散点图',
        image: '/image/report/chart4.png',
        type: 'scatter'
      },
      {
        id: 5,
        title: '销售额变化趋势',
        image: '/image/report/chart5.png',
        type: 'line'
      },
      {
        id: 6,
        title: '用户增长对比',
        image: '/image/report/chart6.png',
        type: 'bar'
      }
    ];
    
    this.setData({
      chartList: mockChartList
    });
  },

  /**
   * 计算高度
   */
  calculateHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    // 预留头部、搜索框、入口区域的高度
    const availableHeight = windowHeight - 300;
    
    this.setData({
      listScrollHeight: Math.max(availableHeight, 400)
    });
  },

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    // 跳转到搜索页面，参考thinkTankList的实现
    wx.navigateTo({
      url: '/childSubpackage/pages/searchPage/index?type=chart'
    });
  },

  /**
   * 专题入口点击事件
   */
  onZtItemTap(e) {
    const type = e.currentTarget.dataset.type;
    
    wx.showModal({
      title: '功能提示',
      content: `${this.getZtItemName(type)}功能正在开发中，敬请期待！`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 获取专题入口名称
   */
  getZtItemName(type) {
    const nameMap = {
      chart_analysis: '图表分析',
      data_visual: '数据可视化',
      trend_chart: '趋势图表',
      compare_chart: '对比图表'
    };
    return nameMap[type] || '未知功能';
  },

  /**
   * 图表点击事件
   */
  onChartClick(e) {
    const {item} = e.detail;
    
    if (!this.data.isLogin) {
      app.route(this, '/pages/login/login');
      return;
    }
    
    wx.showModal({
      title: '查看图表',
      content: `即将查看图表：${item.title}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '研报图表 - 数据可视化分析',
      path: '/childSubpackage/pages/ReportTuBiao/index',
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/report/r_ztbg.png'
    };
  }
});
