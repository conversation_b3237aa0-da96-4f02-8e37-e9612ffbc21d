.chart-grid-container {
  padding: 32rpx;
  overflow-y: auto;
}

.chart-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.chart-item {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  
  .chart-image {
    width: 100%;
    height: 200rpx;
    background: #f5f6f7;
  }
  
  .chart-info {
    padding: 24rpx;
    
    .chart-title {
      display: block;
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      margin-bottom: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .chart-type {
      font-size: 22rpx;
      color: #999;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
