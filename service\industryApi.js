import request from './request';
import {newIndustryUrl} from './config';

// 热点产业链统计
const hotStatisticsApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/hotStatistics`,
    method: 'GET'
  });
};
// 经典产业链统计
const classicStatisticsApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/classicStatistics`,
    method: 'GET'
  });
};

// 产业链图谱统计
const originalStatisticsApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/originalStatistics`,
    method: 'GET'
  });
};

// 热点产业链 - 大类 -下拉/列表
const hotIndustryTypeSelectorApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/hotIndustryTypeSelector`,
    method: 'GET'
  });
};

// 经典产业链 - 大类 -下拉/列表
const classicIndustryTypeSelectorApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/classicIndustryTypeSelector`,
    method: 'GET'
  });
};

// 产业链图谱 - 大类(集群) -下拉/列表
const originalIndustryClusterSelectorApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/originalIndustryClusterSelector`,
    method: 'GET'
  });
};
// 热点产业链 - 全部产业 - 产业链列表
const hotIndustryExpandListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/hotIndustryExpandList`,
    method: 'POST',
    data
  });
};
// 经典产业链 - 全部产业 - 产业链列表
const classicIndustryExpandListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/classicIndustryExpandList`,
    method: 'POST',
    data
  });
};

// 产业链图谱 - 全部产业 - 产业链列表
const originalIndustryExpandListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/originalIndustryExpandList`,
    method: 'POST',
    data
  });
};

// 产业链图谱 - 全部产业 - 已购买的产业链
const originalIndustryPurchasedListApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/originalIndustryPurchasedList`,
    method: 'GET'
  });
};

// 热点产业链 - 首页简单列表
const hotIndustrySimpleListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/hotIndustrySimpleList`,
    method: 'POST',
    data
  });
};

// 经典产业链 - 首页简单列表
const classicIndustrySimpleListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/classicIndustrySimpleList`,
    method: 'POST',
    data
  });
};
// 产业链图谱- 首页简单列表
const originalIndustrySimpleListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/originalIndustrySimpleList`,
    method: 'POST',
    data
  });
};

// 产业链最近查看

const userBrowseHistoryApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/userBrowseHistory`,
    method: 'GET'
  });
};
// 搜索页面 - 热门产业领域(热点产业 + 经典领域 + 产业链图谱)
const pastMonthSearchApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/pastMonthSearch`,
    method: 'GET'
  });
};

// 搜索页面 - 热门产业领域(热点产业 + 经典领域 + 产业链图谱)
const cleanBrowseHistoryApi = () => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/cleanBrowseHistory`,
    method: 'DELETE'
  });
};

// 产业链图谱 - 单个产业统计信息
const singleChainStatisticApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/singleChainStatistic`,
    method: 'POST',
    data
  });
};

// 产业链图谱 - 产业节点数量信息(不含上中下游)
const itypeApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/singleChainNodeNumTreeWithOutItype`,
    method: 'POST',
    data
  });
};
// 产业链图谱 - (含上中下游)
const singleChainNodeNumTreeApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/singleChainNodeNumTree`,
    method: 'POST',
    data
  });
};

// 产业链图谱 - 产业节点区域数量排行
const regionChainEntRankApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/regionChainEntRank`,
    method: 'POST',
    data
  });
};
// 产业链图谱 - 产业节点区域数量排行 --新的 之前老的要替换
const appChainRegionEntDataAnalysisApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/appChainRegionEntDataAnalysis`,
    method: 'POST',
    data
  });
};

// 产业链图谱 - 单产业链重点企业统计
const singleChainKeyEntNumApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/singleChainKeyEntNum`,
    method: 'POST',
    data
  });
};
// 地图热力图
const mapHot = data => {
  return request({
    url: `${newIndustryUrl}/industry/regionalAnalysis`,
    method: 'POST',
    data
  });
};

// 判断产业链图谱是否已购买
const isPurchased = chain_code => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/isOriginalIndustryPurchased/${chain_code}`,
    method: 'GET'
  });
};

// 产业链图谱 - 上中下游 下拉弹窗
const singleChainNodeTreeApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/singleChainNodeTree`,
    method: 'POST',
    data
  });
};

//产业研报  -分页查询
const getReportPageListApi = source => {
  let url = '';
  const {type, ...data} = source;
  if (type == 'hot') {
    // 热点
    url = `${newIndustryUrl}/AppIndustry/hotIndustryReport`;
  } else if (type == 'classic') {
    // 经典
    url = `${newIndustryUrl}/AppIndustry/classicIndustryReport`;
  } else if (type == 'chainMap') {
    // 产业图谱
    url = `${newIndustryUrl}/AppIndustry/researchReportPageList`;
  }
  if (!url) return;
  return request({
    url,
    method: 'POST',
    data
  });
};

// 产业专题列表 - 分页查询
const getTopicPageListApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/topicPageList`,
    method: 'POST',
    data
  });
};

// 产业专题搜索
const searchTopicApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/searchTopic`,
    method: 'POST',
    data
  });
};
// -------历史浏览相关
const addHistory = data => {
  //新增搜索历史
  return request({
    url: `${newIndustryUrl}/search/history`,
    method: 'POST',
    data
  });
};
const getHistory = type => {
  //查询最近搜索
  return request({
    url: `${newIndustryUrl}/search/history?model_type=${type}`,
    method: 'GET'
  });
};
const clearHistory = type => {
  //清空最近搜索
  return request({
    url: `${newIndustryUrl}/search/history?model_type=${type}`,
    method: 'DELETE'
  });
};
const addBevHis = data => {
  //新增浏览历史
  return request({
    url: `${newIndustryUrl}/behavior/history`,
    method: 'POST',
    data
  });
};
const detBevHis = type => {
  //删除浏览历史
  return request({
    url: `${newIndustryUrl}/behavior/history?model_type=${type}`,
    method: 'DELETE'
  });
};
const getBevHis = type => {
  //获取浏览历史
  return request({
    url: `${newIndustryUrl}/behavior/history?model_type=${type}`,
    method: 'GET'
  });
};
// ---------------------

// 企业数量趋势 （近五年） 柱状图1
const appChainEntTrendApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/appChainEntTrend`,
    method: 'POST',
    data
  });
};

// 产业通app专利类别分布  饼图
const chainPatentDistributeApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/chainPatentDistribute`,
    method: 'POST',
    data
  });
};
// 产业通app专利数量趋势 柱状图
const appPatentNumTrendApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/appPatentNumTrend`,
    method: 'POST',
    data
  });
};

// 上市企业首页概况
const chainListedEntInfoStatisticApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/chainListedEntInfoStatistic`,
    method: 'POST',
    data
  });
};

//上市企业数量趋势
const appChainListedEntTrendApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/appChainListedEntTrend`,
    method: 'POST',
    data
  });
};

// 上市企业 上市企业区域数量排行
const regionChainListedEntRankApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/regionChainListedEntRank`,
    method: 'POST',
    data
  });
};
// app专利统计
const chainPatentSummaryApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/chainPatentSummary`,
    method: 'POST',
    data
  });
};

// 产业领域概况--查看企业名单  上面统计数据 聚合--包括热点，产业，经典
const getStaticApi = source => {
  let url = '';
  const {type, ...data} = source;
  if (type == 'hot') {
    // 热点
    url = `${newIndustryUrl}/AppIndustry/hotChainStatistic`;
  } else if (type == 'classic') {
    // 经典
    url = `${newIndustryUrl}/AppIndustry/classicChainStatistic`;
  } else if (type == 'chainMap') {
    // 产业图谱
    url = `${newIndustryUrl}/AppIndustry/singleChainStatistic`;
  }
  if (!url) return;
  return request({
    url,
    method: 'POST',
    data
  });
};

// app专利统计
const regionChainDetailCompareApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/regionChainDetailCompare`,
    method: 'POST',
    data
  });
};
// app专利统计
const applyOpenOriginalIndustryApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/applyOpenOriginalIndustry`,
    method: 'POST',
    data
  });
};
// 地图企业总量
const appChainAreaEntNumApi = data => {
  return request({
    url: `${newIndustryUrl}/AppIndustry/appChainAreaEntNum`,
    method: 'POST',
    data
  });
};

module.exports = {
  hotStatisticsApi,
  classicStatisticsApi,
  originalStatisticsApi,
  hotIndustryTypeSelectorApi,
  classicIndustryTypeSelectorApi,
  originalIndustryClusterSelectorApi,
  hotIndustryExpandListApi,
  classicIndustryExpandListApi,
  originalIndustryExpandListApi,
  originalIndustryPurchasedListApi,
  hotIndustrySimpleListApi,
  classicIndustrySimpleListApi,
  originalIndustrySimpleListApi,
  userBrowseHistoryApi,
  pastMonthSearchApi,
  cleanBrowseHistoryApi,
  singleChainStatisticApi,
  itypeApi,
  regionChainEntRankApi,
  singleChainKeyEntNumApi,
  mapHot,
  singleChainNodeNumTreeApi,
  isPurchased,
  singleChainNodeTreeApi,
  getReportPageListApi,
  getTopicPageListApi,
  searchTopicApi,
  addHistory,
  getHistory,
  clearHistory,
  addBevHis,
  detBevHis,
  getBevHis,
  appChainRegionEntDataAnalysisApi,
  appChainEntTrendApi,
  chainPatentDistributeApi,
  appPatentNumTrendApi,
  chainListedEntInfoStatisticApi,
  appChainListedEntTrendApi,
  regionChainListedEntRankApi,
  getStaticApi,
  chainPatentSummaryApi,
  regionChainDetailCompareApi,
  applyOpenOriginalIndustryApi,
  appChainAreaEntNumApi
};
