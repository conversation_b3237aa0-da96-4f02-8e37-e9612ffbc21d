Page({
  data: {
    searchValue: '', // 搜索输入值
    searchHistory: [], // 搜索历史
    showSuggestions: false, // 是否显示搜索建议
    showSearchResult: false, // 是否显示搜索结果
    searchResultCount: 0, // 搜索结果数量
    topicList: [], // 专题列表
    inputShowed: false, // 输入框是否聚焦

    listScrollHeight: 600 // 列表滚动区域高度
  },

  onLoad(options) {
    this.initPageHeight();
    this.initData();
    this.loadSearchHistory();
  },

  // 初始化页面高度
  initPageHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    const searchContainerHeight = 80; // 搜索容器高度 (28+16+16+40)rpx 转换为px

    // 基础高度：窗口高度 - 搜索容器高度
    let baseHeight = windowHeight - searchContainerHeight;

    this.setData({
      listScrollHeight: baseHeight
    });
  },

  // 动态计算列表高度
  calculateListHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    const searchContainerHeight = 80; // 搜索容器高度

    let availableHeight = windowHeight - searchContainerHeight;

    // 如果显示搜索结果头部，减去其高度
    if (this.data.showSearchResult) {
      availableHeight -= 80; // 搜索结果头部高度
    }

    this.setData({
      listScrollHeight: Math.max(availableHeight, 300) // 最小高度300px
    });
  },

  // 初始化数据
  initData() {
    // 加载初始专题数据
    const allTopics = this.generateAllMockTopics();
    this.setData({
      topicList: allTopics,
      searchResultCount: allTopics.length
    });
  },

  // 加载搜索历史
  loadSearchHistory() {
    try {
      let history = wx.getStorageSync('search_history_report') || [];

      // 如果没有历史记录，添加一些mock数据
      if (history.length === 0) {
        history = [
          '新能源汽车',
          '人工智能',
          '5G通信',
          '区块链技术',
          '数字化转型'
        ];
        // 保存mock数据到本地存储
        wx.setStorageSync('search_history_report', history);
      }

      this.setData({searchHistory: history});
    } catch (e) {
      console.error('加载搜索历史失败:', e);
      // 如果读取失败，使用默认mock数据
      this.setData({
        searchHistory: [
          '新能源汽车',
          '人工智能',
          '5G通信',
          '区块链技术',
          '数字化转型'
        ]
      });
    }
  },

  // 保存搜索历史
  saveSearchHistory(keyword) {
    if (!keyword.trim()) return;

    try {
      let history = wx.getStorageSync('search_history_report') || [];

      // 移除重复项
      history = history.filter(item => item !== keyword);

      // 添加到开头
      history.unshift(keyword);

      // 限制历史记录数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }

      wx.setStorageSync('search_history_report', history);
      this.setData({searchHistory: history});
    } catch (e) {
      console.error('保存搜索历史失败:', e);
    }
  },

  // 搜索输入事件
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({
      searchValue: value,
      showSearchResult: value.trim().length > 0,
      showSuggestions: false
    });

    if (value.trim().length > 0) {
      this.performSearch(value);
    } else {
      // 清空搜索，显示所有数据
      const allTopics = this.generateAllMockTopics();
      this.setData({
        topicList: allTopics,
        searchResultCount: allTopics.length
      });
    }

    // 重新计算高度
    this.calculateListHeight();
  },

  // 搜索框聚焦事件
  onSearchFocus() {
    if (!this.data.searchValue.trim()) {
      this.setData({showSuggestions: true});
    }
  },

  // 搜索框失焦事件
  onSearchBlur() {
    // 延迟隐藏，避免点击历史记录时立即隐藏
    setTimeout(() => {
      this.setData({
        showSuggestions: false,
        inputShowed: false
      });
    }, 200);
  },

  // 清空输入框
  onClear() {
    const allTopics = this.generateAllMockTopics();
    this.setData({
      searchValue: '',
      showSearchResult: false,
      showSuggestions: false,
      topicList: allTopics,
      searchResultCount: allTopics.length
    });
    this.calculateListHeight();
  },

  // 清空历史记录
  onClearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索记录吗？',
      success: res => {
        if (res.confirm) {
          try {
            wx.removeStorageSync('search_history_report');
            this.setData({searchHistory: []});
            wx.showToast({
              title: '已清空',
              icon: 'success'
            });
          } catch (e) {
            console.error('清空搜索历史失败:', e);
          }
        }
      }
    });
  },

  // 页面点击事件
  onPageTap() {
    // 可以在这里处理页面点击事件
  },

  // 搜索确认事件
  onSearchConfirm() {
    const keyword = this.data.searchValue.trim();
    if (!keyword) return;

    this.saveSearchHistory(keyword);
    this.performSearch(keyword);
    this.setData({
      showSuggestions: false,
      showSearchResult: true
    });
  },

  // 执行搜索
  performSearch(keyword) {
    const allTopics = this.generateAllMockTopics();

    // 构造一个不区分大小写的正则
    const regex = new RegExp(keyword, 'i');

    const filteredTopics = allTopics.filter(topic => regex.test(topic.title));

    this.setData({
      topicList: filteredTopics,
      searchResultCount: filteredTopics.length
    });

    this.calculateListHeight();
  },

  // 生成所有模拟数据用于搜索
  generateAllMockTopics() {
    const topics = [
      {
        id: 1,
        title: '新能源汽车产业发展趋势',
        subtitle: '50'
      },
      {
        id: 2,
        title: '人工智能技术应用',
        subtitle: '50'
      },
      {
        id: 3,
        title: '5G通信技术革命',
        subtitle: '50'
      },
      {
        id: 4,
        title: '绿色环保产业链',
        subtitle: '50'
      },
      {
        id: 5,
        title: '数字化转型趋势',
        subtitle: '50'
      },
      {
        id: 6,
        title: '生物医药创新发展',
        subtitle: '50'
      },
      {
        id: 7,
        title: '智能制造产业升级',
        subtitle: '50'
      },
      {
        id: 8,
        title: '区块链技术应用',
        subtitle: '50'
      },
      {
        id: 9,
        title: '云计算服务发展',
        subtitle: '50'
      },
      {
        id: 10,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 11,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 12,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 13,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 14,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 15,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 16,
        title: '物联网产业生态',
        subtitle: '50'
      },
      {
        id: 17,
        title: '物联网产业生态',
        subtitle: '50'
      }
    ];

    return topics.map(topic => ({
      ...topic,
      image: '/image/report/r_ztzw.png'
    }));
  },

  // 选择历史记录
  onSelectHistory(e) {
    const keyword = e.currentTarget.dataset.keyword;
    this.setData({
      searchValue: keyword,
      showSuggestions: false,
      showSearchResult: true
    });
    this.performSearch(keyword);
  },

  // 进入AI分析
  onEnterAI() {
    wx.showToast({
      title: 'AI功能开发中',
      icon: 'none'
    });
  },

  // 问一问功能
  onAskQuestion() {
    wx.showToast({
      title: '问答功能开发中',
      icon: 'none'
    });
  },

  // 点击专题卡片
  onTopicTap(e) {
    const item = e.currentTarget.dataset.item;
    wx.showToast({
      title: `查看专题: ${item.title}`,
      icon: 'none'
    });

    // 这里可以跳转到专题详情页
    // wx.navigateTo({
    //   url: `/pages/topicDetail/index?id=${item.id}`
    // });
  }
});
