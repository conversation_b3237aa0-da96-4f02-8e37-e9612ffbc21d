.page {
  min-height: 100vh;
  background: #f7f7f7;
}

// 第一行文字 居中
.header-text {
  text-align: center;
  padding: 40rpx 0 32rpx;
  background: #fff;

  .header-title {
    font-family:
      PingFang SC,
      PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #9b9eac;
  }
}

// 第二行 搜索框 参考thinkTankList
.search-container {
  padding: 0 32rpx 32rpx;
  background: #fff;

  .search-box {
    display: flex;
    align-items: center;
    height: 80rpx;
    background: #f5f6f7;
    border-radius: 40rpx;
    padding: 0 32rpx;

    .search-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }

    .search-placeholder {
      font-size: 28rpx;
      color: #9b9eac;
    }
  }
}

// 第三行 研报入口 参考pages/report/report的zt-container
.zt-container {
  padding: 40rpx;
  background: #fff;
  margin-top: 20rpx;

  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;

  .zt-item {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .zt-icon {
      width: 48rpx;
      height: 48rpx;
      margin-right: 16rpx;
    }

    .zt-content {
      flex: 1;

      .zt-title {
        display: block;
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        margin-bottom: 4rpx;
      }

      .zt-desc {
        font-size: 22rpx;
        color: #999;
      }
    }
  }
}

// 第四行 图表列表容器
.chart-list-container {
  margin-top: 20rpx;
  background: #fff;
  min-height: 400rpx;
}
